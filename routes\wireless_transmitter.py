from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required
from models.database import db
from models.wireless_transmitter import WirelessTransmitter
from sqlalchemy import or_, and_
from datetime import datetime
import logging

# 创建蓝图
wireless_transmitter_bp = Blueprint('wireless_transmitter', __name__, url_prefix='/wireless_transmitter')

# 获取日志记录器
logger = logging.getLogger(__name__)

@wireless_transmitter_bp.route("/")
@login_required
def wireless_transmitter_list():
    """无线充电发射端管理页面"""
    try:
        # 获取发射端统计信息
        total_transmitters = WirelessTransmitter.query.count()
        
        # 按硬件版本统计
        bike_count = WirelessTransmitter.query.filter_by(hardware_version=1).count()
        tricycle_count = WirelessTransmitter.query.filter_by(hardware_version=2).count()
        
        stats = {
            "total": total_transmitters,
            "bike": bike_count,
            "tricycle": tricycle_count
        }
        
        return render_template(
            "wireless_transmitter.html",
            transmitters=[],  # 通过Ajax加载
            stats=stats
        )
    except Exception as e:
        logger.error(f"访问发射端管理页面失败: {e}")
        flash(f"访问发射端管理页面失败: {str(e)}", "error")
        return redirect(url_for('main.index'))

@wireless_transmitter_bp.route("/api/transmitters")
@login_required
def get_transmitters_api():
    """获取发射端列表API（支持分页、搜索、筛选）"""
    try:
        # 获取分页参数
        page = request.args.get("page", 1, type=int)
        per_page = request.args.get("per_page", 20, type=int)
        
        # 获取搜索和筛选参数
        search = request.args.get("search", "").strip()
        hardware_filter = request.args.get("hardware", "all")
        topic_filter = request.args.get("topic", "").strip()
        
        # 构建查询
        query = WirelessTransmitter.query
        
        # 搜索条件
        if search:
            search_conditions = []
            # 尝试按发射端ID搜索（精确匹配或模糊匹配）
            try:
                search_id = int(search)
                search_conditions.append(WirelessTransmitter.transmitter_id == search_id)
            except ValueError:
                pass
            
            # 按备注搜索
            search_conditions.append(WirelessTransmitter.transmitter_remark.ilike(f"%{search}%"))
            
            # 按主题搜索
            search_conditions.append(WirelessTransmitter.topic.ilike(f"%{search}%"))
            
            # 按中控ID搜索
            try:
                controller_id = int(search)
                search_conditions.append(WirelessTransmitter.controller_id == controller_id)
            except ValueError:
                pass
            
            if search_conditions:
                query = query.filter(or_(*search_conditions))
        
        # 硬件版本筛选
        if hardware_filter != "all":
            try:
                hardware_version = int(hardware_filter)
                query = query.filter(WirelessTransmitter.hardware_version == hardware_version)
            except ValueError:
                pass
        
        # 主题筛选
        if topic_filter:
            query = query.filter(WirelessTransmitter.topic.ilike(f"%{topic_filter}%"))
        
        # 排序
        query = query.order_by(WirelessTransmitter.register_date.desc())
        
        # 分页
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        transmitters = pagination.items
        
        # 转换为字典格式
        transmitters_data = []
        for transmitter in transmitters:
            transmitter_dict = transmitter.to_dict()
            transmitters_data.append(transmitter_dict)
        
        return jsonify({
            "success": True,
            "transmitters": transmitters_data,
            "pagination": {
                "page": pagination.page,
                "pages": pagination.pages,
                "per_page": pagination.per_page,
                "total": pagination.total,
                "has_prev": pagination.has_prev,
                "has_next": pagination.has_next
            }
        })
        
    except Exception as e:
        logger.error(f"获取发射端列表失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取发射端列表失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/transmitters", methods=["POST"])
@login_required
def add_transmitter_api():
    """添加发射端API"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        if not data.get("transmitter_id"):
            return jsonify({
                "success": False,
                "message": "发射端ID不能为空"
            }), 400
        
        transmitter_id = int(data["transmitter_id"])
        
        # 检查发射端ID是否已存在
        existing = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
        if existing:
            return jsonify({
                "success": False,
                "message": f"发射端ID {transmitter_id} 已存在"
            }), 400
        
        # 处理软件版本
        software_version = 0
        if data.get("software_version_string"):
            software_version = WirelessTransmitter.version_string_to_int(data["software_version_string"])
        
        # 创建新发射端
        transmitter = WirelessTransmitter(
            transmitter_id=transmitter_id,
            transmitter_remark=data.get("transmitter_remark", ""),
            hardware_version=int(data.get("hardware_version", 1)),
            software_version=software_version,
            controller_id=int(data["controller_id"]) if data.get("controller_id") else None,
            topic=data.get("topic", "")
        )
        
        db.session.add(transmitter)
        db.session.commit()
        
        logger.info(f"成功添加发射端: {transmitter_id}")
        
        return jsonify({
            "success": True,
            "message": "发射端添加成功",
            "transmitter": transmitter.to_dict()
        })
        
    except ValueError as e:
        return jsonify({
            "success": False,
            "message": f"数据格式错误: {str(e)}"
        }), 400
    except Exception as e:
        db.session.rollback()
        logger.error(f"添加发射端失败: {e}")
        return jsonify({
            "success": False,
            "message": f"添加发射端失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/transmitters/<int:transmitter_id>", methods=["PUT"])
@login_required
def update_transmitter_api(transmitter_id):
    """更新发射端API"""
    try:
        transmitter = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
        if not transmitter:
            return jsonify({
                "success": False,
                "message": "发射端不存在"
            }), 404
        
        data = request.get_json()
        
        # 更新字段
        if "transmitter_remark" in data:
            transmitter.transmitter_remark = data["transmitter_remark"]
        
        if "hardware_version" in data:
            transmitter.hardware_version = int(data["hardware_version"])
        
        if "software_version_string" in data:
            transmitter.software_version = WirelessTransmitter.version_string_to_int(data["software_version_string"])
        
        if "controller_id" in data:
            transmitter.controller_id = int(data["controller_id"]) if data["controller_id"] else None
        
        if "topic" in data:
            transmitter.topic = data["topic"]
        
        transmitter.updated_at = datetime.now()
        
        db.session.commit()
        
        logger.info(f"成功更新发射端: {transmitter_id}")
        
        return jsonify({
            "success": True,
            "message": "发射端更新成功",
            "transmitter": transmitter.to_dict()
        })
        
    except ValueError as e:
        return jsonify({
            "success": False,
            "message": f"数据格式错误: {str(e)}"
        }), 400
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新发射端失败: {e}")
        return jsonify({
            "success": False,
            "message": f"更新发射端失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/transmitters/<int:transmitter_id>", methods=["DELETE"])
@login_required
def delete_transmitter_api(transmitter_id):
    """删除发射端API"""
    try:
        transmitter = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
        if not transmitter:
            return jsonify({
                "success": False,
                "message": "发射端不存在"
            }), 404
        
        db.session.delete(transmitter)
        db.session.commit()
        
        logger.info(f"成功删除发射端: {transmitter_id}")
        
        return jsonify({
            "success": True,
            "message": "发射端删除成功"
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除发射端失败: {e}")
        return jsonify({
            "success": False,
            "message": f"删除发射端失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/transmitters/<int:transmitter_id>")
@login_required
def get_transmitter_api(transmitter_id):
    """获取单个发射端信息API"""
    try:
        transmitter = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
        if not transmitter:
            return jsonify({
                "success": False,
                "message": "发射端不存在"
            }), 404
        
        return jsonify({
            "success": True,
            "transmitter": transmitter.to_dict()
        })
        
    except Exception as e:
        logger.error(f"获取发射端信息失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取发射端信息失败: {str(e)}"
        }), 500
